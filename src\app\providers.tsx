"use client";
import { ReactNode } from "react";
import { Provider } from "react-redux";
import { store } from "@/lib/redux/store";
import { QueryClientProvider } from "@tanstack/react-query";
import queryClient from "@/lib/tansack-query/queryClient";
import { PersistGate } from "redux-persist/integration/react";
import { persistor } from "@/lib/redux/store";
import { LayoutProvider } from "@/context"; // Import our LayoutProvider
import { Toaster } from "@/components/ui/sonner";
import { CookiesProvider } from 'react-cookie';

const Providers = ({ children }: { children: ReactNode }) => {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <QueryClientProvider client={queryClient}>
        <CookiesProvider defaultSetOptions={{ path: '/' }}>
          <LayoutProvider>
                {children}
                <Toaster  position="top-right" duration={3000}  />
          </LayoutProvider>
          </CookiesProvider>
        </QueryClientProvider>
      </PersistGate>
    </Provider>
  );
};

export default Providers;
