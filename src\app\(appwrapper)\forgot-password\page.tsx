"use client";
import Image from "next/image";
import { useState } from "react";
import { useTranslation } from 'react-i18next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod"; 
import { Form } from "@/components/ui/form";
import { AppButton, AppFormItem } from "@/components"; 
import logo from "@/assets/icons/logo.png"
import UkFlag from "@/assets/icons/uk-flag.svg"
import roundLeft from "@/assets/icons/round-left.png"
import haFlag from "@/assets/images/hausa-logo.png"
import farmBg from "@/assets/images/farm-bg.svg"
import {
  usePasswordResetRequest,
  usePasswordReset
} from "@/hooks";
import { AnimatePresence, motion } from "framer-motion";
import { useLayoutContext } from "@/context";
import { forgotEmailValidation, forgotOtpValidation, password<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/schema/forgotPassword";


const ForgotPassword = () => {
  const [ step, setStep] = useState(1)
  const { setAppData, appData } = useLayoutContext()
  const { t, i18n } = useTranslation();
  const { mutate: mutatePasswordReset, isPending: isPendingPasswordReset } = usePasswordReset()

  const emailForm = useForm({
    resolver: zodResolver(forgotEmailValidation),
    defaultValues: {
        username: "",
    }
  })
  const otpForm = useForm({
    resolver: zodResolver(forgotOtpValidation),
    defaultValues: {
      otp: "",
    }
  })
  const passwordChangeForm = useForm({
    resolver: zodResolver(passwordChangeValidaton),
    defaultValues: {
        otp: "",
        username: "",
        newPassword: "",
        confirmPassword: ""
    },
    mode: 'onBlur',
    reValidateMode: 'onChange',
  })
  const handleNext = () => {
    setStep((prev) => prev + 1)
  }
  const { mutate: mutatePasswordRequest, isPending: isPendingPasswordRequest } = usePasswordResetRequest(handleNext)
  const onSubmitEmailForm = (values: {username: string}) => {
    mutatePasswordRequest({
      username: values.username.toLowerCase(),
    })
  }
  const onSubmitPasswordChange = (values: {
    newPassword: string;
  }) => {
    mutatePasswordReset({
      token: otpForm.watch('otp'),
      username: emailForm.watch('username'),
      newPassword: values.newPassword
    })
  }
  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
  };
  return (
    <main id="safe-focus-element">
      <div className="grid md:grid-cols-2 h-[100vh] relative">
        {/* Left Section */}
        <div className="hidden md:block border-r border-primary-1 p-[4.5rem]">
          <Image src={logo} alt="cafi logo" />
          <h2 className="font-feather text-4xl text-primary-1 mt-36"> {t('login.hero')} </h2>
        </div>

        {/* Right Section */}
        <div className=" col-span-1 container mx-auto md:max-w-[95%] grid h-[90vh] z-20">
          <div className="mt-[2.5rem] md:justify-self-end">
            {/* <div className="flex items-center gap-[0.857rem]  rounded-[0.3125rem] w-fit"> */}
            <Select defaultValue={appData.language}
             onValueChange={(value: string ) => {
              changeLanguage(value)
              setAppData({
              ...appData,
              language: value
            })}}>
              <SelectTrigger className="w-fit">
                <SelectValue placeholder={appData.language === "en" ? 'English' : 'Hausa'}/>
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value='en'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={UkFlag} alt="uk flag"/>
                  <span className="text-[0.875rem] text-gray  "> English </span> 
                  </span>
                </SelectItem>
                <SelectItem value='ha'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={haFlag} alt="uk flag" className="w-[14px] h-[14px]" />
                  <span className="text-[0.875rem] text-gray  "> Hausa </span> 
                  </span>
                </SelectItem>
              </SelectContent>             
            </Select>
            {/* </div>  */}
          </div>
          <div className="flex-grow flex justify-center">
            <div className="w-full relative overflow-hidden">
              <AnimatePresence mode="wait">
                { step === 1 ? (
                    <motion.div
                      key="login-screen"
                      initial={{ x: "-100%", opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: "-100%", opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="w-full flex flex-col"
                      >
                      <div className="grid h-[70vh] auto-rows-[min-content_auto]">
                        <div className="space-y-2">
                            <h1 className="font-fredoka font-medium text-black-1 text-2xl">
                                Enter Your Registered Email
                            </h1>
                            <p className="font-blogger text-sm font-medium text-black-1">
                                Please enter your registered email address and click &apos;Next&apos; to continue.
                            </p>
                        </div>
                        <Form {...emailForm}>
                        <form className="space-y-4 mt-8">
                            <AppFormItem
                            control={emailForm.control}
                            name="username"
                            inputType="textInput"
                            label={t('login.emailLabel')}
                            placeholder=""
                            />
        
                        </form>
                        </Form>
                        <AppButton 
                        text='Next' 
                        isLoading={isPendingPasswordRequest}
                        onClick={emailForm.handleSubmit(onSubmitEmailForm)}
                        iconSuff={ <Image src={roundLeft} alt="round left" className="" />}
                        className="self-end"
                        disabled={!emailForm.formState.isValid}
                        />
                     </div>
                    </motion.div>
                  ) : step === 2 ? (
                    <motion.div
                      key="otp-screen"
                      initial={{ x: "100%", opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: "100%", opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="w-full flex flex-col"
                    >
                    <div className="grid h-[70vh] auto-rows-[min-content_auto]">
                        <div className="">
                            <h1 className="font-fredoka font-medium text-black-1 text-2xl"> Enter the OTP sent to your email </h1>
                            <p className="font-blogger text-sm font-medium text-black-1">Please enter the OTP that was sent to your email address and click &apos;Next&apos; to proceed.</p>
                        </div>
                            <Form
                            {...otpForm}
                            >
                              <form  className="space-y-4 mt-8">
                               <AppFormItem
                                  control={otpForm.control}
                                  name="otp"
                                  inputType="otpInput"
                                  otpLength={6} // Customize OTP length
                                  isNumericOnly={true}
                                />
                              </form>                           
                            </Form>
                            <AppButton 
                              text='Next'
                              onClick={() => handleNext()}
                              iconSuff={ <Image src={roundLeft} alt="round left" className="" />}
                              className="self-end"
                              disabled={!otpForm.formState.isValid}
                            />
                    </div>
                    </motion.div>
                  ) : (
                    <motion.div
                    key="password-change"
                    initial={{ x: "100%", opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: "100%", opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="w-full"
                    >
                      <div className="grid h-[70vh] auto-rows-[min-content_auto]">
                        <div className="space-y-2">
                          <h1 className="font-fredoka font-medium text-black-1 text-2xl"> Please enter your new password</h1>
                            <h2 className="font-blogger text-sm font-medium text-black-1"> Set your new password and then click &apos;Next&apos; to proceed. </h2>
                            <p className="font-medium text-gray font-blogger">Your password must have at least 8 characters, including uppercase and lowercase letters, numbers, and special symbols. Click &apos;Next&apos; to continue.</p>
                        </div>
                        <Form
                        {...passwordChangeForm}
                        >
                          <form className="space-y-4 mt-8">
                          <AppFormItem 
                          control={passwordChangeForm.control} 
                          name="newPassword" 
                          inputType="passwordInput"
                          label="Enter Password" 
                          />
                          <AppFormItem 
                            control={passwordChangeForm.control} 
                            name="confirmPassword" 
                            inputType="passwordInput" 
                            label="Confirm Password" 
                          />
                          </form>
                        </Form>
                        <AppButton 
                        text='Next'
                        isLoading={isPendingPasswordReset}
                        onClick={passwordChangeForm.handleSubmit(onSubmitPasswordChange)}
                        iconSuff={ <Image src={roundLeft} alt="round left" className="" />}
                        className="self-end"
                        disabled={!passwordChangeForm .formState.isValid}

                        />
                      </div>
                    </motion.div>
                  )
                }
              </AnimatePresence>
            </div>
          </div>
        </div>
        
        <div className="absolute w-full h-[35%] bottom-0 z-10">
            <Image src={farmBg} alt="farmbg" className="size-full object-cover" />
        </div>
      </div>
    </main>
  );
}

export default ForgotPassword;
