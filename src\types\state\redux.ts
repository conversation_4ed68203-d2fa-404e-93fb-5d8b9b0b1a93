import type { UserConfigs } from "./auth";

export type AuthState = {
  user: {
    token: string;
    configs: UserConfigs;
    managedOrganization:any
  };
  isAccessDenied: boolean;
  isSessionExpired: boolean;
  isSomethingWrong: boolean;
};

export type SidebarState = {
  isSidebarOpen: boolean;
};

export type DialogState = {
  isConfirmationDialogOpen: boolean;
  onNext?: () => void;
  successMessage: string;
  isSuccessDialogOpen: boolean;
  isTrackPaymentOpen: boolean;
  isMemberPaymentDetailsOpen: boolean;
  isBulkMemberPaymentOpen: boolean;
  isFloatPaymentDetailsOpen: boolean;
  isNotificationsOpen: boolean;
  isTransactionDetailsOpen: boolean;
  failedPaymentMessage: string;
  isFailedPaymentDialogOpen: boolean;
  isLogDnpFormOpen: boolean;
  isResolveDnpFormOpen: boolean;
};
