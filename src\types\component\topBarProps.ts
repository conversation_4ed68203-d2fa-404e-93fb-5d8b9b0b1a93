import { FC } from "react";
import { FilterProps } from "./filterProps";

export type TopBarProps = {
  showSearch?: boolean;
  showFilter?: boolean;
  className?: string;
  isError?: boolean;
  searchPlaceholder?: string;
  handleSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  Filter?: FC<FilterProps>;
  showExport?: boolean;
  isExporting?: boolean;
  hasSidebar?: boolean;
  isLoading?: boolean;
  selectedIds?: (string | number)[];
  handleExport?: () => void;
  filterGroups?: FilterProps["filterGroups"];
  statusMaps?: any;
  countryMap?: any;
  zoneMap?:any;
  regionMap?:any;
  productTypeMap?:any;
  loading?:boolean;
  headerRight?: React.ReactNode;
  topBarWidth?: string
};