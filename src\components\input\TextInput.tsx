import * as React from "react";
import { Input, Textarea } from "../ui";
import { TextInputProps } from "@/types/component";
import { cn } from "@/lib/utils";
import { useState } from "react";
import eyeCloseIcon from "@/assets/icons/eye-close.png"
import eyeOpenIcon from "@/assets/icons/eye-open.svg"
import Image from "next/image";


export const TextInput = React.forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      className,
      placeholder,
      type = "text",
      value,
      disabled,
      onChange,
      onBlur,
      error,
      textTransform = "none", // Default to 'none'
    }: TextInputProps,
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const textTransformClass =
    textTransform === "capitalize"
      ? "capitalize"
      : textTransform === "uppercase"
      ? "uppercase"
      : "";
  
  
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };
    const handleChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      let newValue = e.target.value;

      // Apply text transformation
      switch (textTransform) {
        case "uppercase":
          newValue = newValue.toUpperCase();
          break;
        case "capitalize":
          newValue = newValue.replace(/\b\w/g, (char) => char.toUpperCase());
          break;
        default:
          break; // No transformation
      }

      if (type === "phone") {
        // Allow only digits and an optional '+' at the beginning.
        newValue = newValue.replace(/(?!^\+)[^0-9\n]/g, "");
      } else if (type === "number") {
        // Allow only numeric characters.
        newValue = newValue.replace(/[^0-9]/g, "");
        // Convert to number if possible
        const numericValue = newValue !== "" ? Number(newValue) : "";
        if (onChange) {
          const syntheticEvent = {
            ...e,
            target: {
              ...e.target,
              value: numericValue,
            },
          } as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>;
          onChange(syntheticEvent);
        }
        return; // exit early, since we have handled the change
      }

      // Propagate transformed value (for text, phone, etc.)
      if (onChange) {
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: newValue,
          },
        } as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>;
        onChange(syntheticEvent);
      }
    };

    if (type === "textAreaInput") {
      return (
        <Textarea
          ref={ref as React.Ref<HTMLTextAreaElement>}
          value={value}
          onChange={handleChange}
          onBlur={onBlur}
          placeholder={placeholder}
          className={cn(
            "font-blogger border border-primary-gray bg-foreground placeholder:text-accent-body rounded-md resize-none",
            className
          )}
          disabled={disabled}
          style={{ textTransform: "none" }} // Ensure placeholder remains unaffected
        />
      );
    }

    if (type === "password") {
      return (
        <div className="relative">
          <Input
            type={showPassword ? "text" : "password"}
            placeholder={placeholder ?? "Enter password"}
            value={value}
            onChange={onChange as (e: React.ChangeEvent<HTMLInputElement>) => void}
            onBlur={onBlur}
            disabled={disabled}
            className={`pr-10 ${error ? "border-red-500" : ""} ${textTransformClass} ${className}`}
          />
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
            tabIndex={-1}
          >
            {showPassword ? (
              <Image src={eyeCloseIcon} alt="eye close icon" />
            ) : (
              <Image src={eyeOpenIcon} alt="eye open icon" />
            )}
          </button>
        </div>
      );
    }
    return (
      <Input
        ref={ref}
        value={value}
        onBlur={onBlur}
        placeholder={placeholder}
        type={type}
        className={cn("font-blogger", className, {
          uppercase: textTransform === "uppercase",
          capitalize: textTransform === "capitalize",
        })}
        onChange={handleChange}
        disabled={disabled}
        error={error}
        style={{ textTransform: "none" }} // Ensure placeholder is not transformed
      />
    );
  }
);

TextInput.displayName = "TextInput";