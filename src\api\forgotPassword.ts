import { apiService } from "./api";

export const passwordResetRequest = async (payload: {username: string}) => {
    return await apiService({
        url: `password/reset-request`,
        method: 'post',
        protectedRoute: false,
        requestData: payload
    })
}

export const passwordReset = async (payload: {
    token: string;
    username: string;
    newPassword: string
}) => {
    return await apiService({
        url: `password/reset`,
        method: 'post',
        protectedRoute: false,
        requestData: payload
    })
}
