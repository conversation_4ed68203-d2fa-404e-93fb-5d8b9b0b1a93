"use client";
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Image from "next/image";
import { useSearchParams } from 'next/navigation';
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { AppButton, AppFormItem, withSplashScreen } from "@/components";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";
import logo from "@/assets/icons/logo.png";
import UkFlag from "@/assets/icons/uk-flag.svg";
import haFlag from "@/assets/images/hausa-logo.png"
import farmBg from "@/assets/images/farm-bg.svg";
import { useLayoutContext } from "@/context";
import { useVerifyUser, useGetUserValidation, useSetPassword} from "@/hooks";
import {verifyUserBody, verifyUserValidation, setPasswordValidation, setPasswordBody } from "@/schema";
import { useTranslation } from "react-i18next";

const OnboardingPage = () => {
  const [step, setStep] = useState(1);
  const [userId, setUserId] = useState("");
  const { setAppData, appData } = useLayoutContext();
  const { t, i18n } = useTranslation();
  const searchParams = useSearchParams();
  const invitationId = searchParams.get('invitationId');
  const handleNext = async () => {
      setStep(2);
  };
  const { mutate: mutateVerifyUser, isPending: isPendingVerifyUser } = useVerifyUser(handleNext, setUserId)
  const { mutate: mutateSetPassword, isPending: isPendingSetPassword } = useSetPassword()
  const { data } = useGetUserValidation(invitationId)
  const userVerifyForm = useForm({
    resolver: zodResolver(verifyUserValidation),
    defaultValues: {
      username:  "",
      verificationCode: "",
    },
  });
  const passwordForm = useForm({
    resolver: zodResolver(setPasswordValidation),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });
  const onSubmitVerifyUser = (values: verifyUserBody) => {
    mutateVerifyUser(values)
  }
  const handleFinish = (values: setPasswordBody) => {
    mutateSetPassword({
      password: values.password,
      userId: userId
    })
  };
  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
  };
  useEffect(() => {
    if (data?.data) {
      userVerifyForm.reset({
        username: data.data.username || "",
        verificationCode: data.data.verificationCode || "",
      });
    }
  }, [data, userVerifyForm]);

  return (
    <div className="grid md:grid-cols-2 h-screen relative">
      {/* Left Section */}
      <div className="hidden md:block border-r border-primary-1 p-8 lg:p-16 xl:p-[4.5rem] text-primary-1">
        <Image src={logo} alt="cafi logo" />
        <h2 className="font-feather text-2xl lg:text-3xl xl:text-4xl mt-16 lg:mt-24 xl:mt-36">
          Welcome to CAFI TECH ECOSYSTEM
        </h2>
        <p className="mt-4 lg:mt-6 xl:mt-8">
          Kindly verify your email/phone number by inputting your phone number and verification code.
        </p>
      </div>

      {/* Right Section */}
      <div className=" col-span-1 container mx-auto max-w-[95%] grid h-[90vh] z-20">
      <div className="mt-[2.5rem] md:justify-self-end">
      {/* <div className="flex items-center gap-[0.857rem] py-2 px-2 outline outline-1 outline-gray rounded-[0.3125rem] w-fit">
      <span className="flex items-center gap-[0.35rem]">
              <Image src={UkFlag} alt="uk flag" className="w-3.5 h-3.5" />
              <span className="text-sm text-gray">English</span>
         </span>
            <Image src={dropDown} alt="dropdown" className="w-3 h-3" />
          </div> */}
         <Select defaultValue={appData.language}
             onValueChange={(value: string ) => {
              changeLanguage(value)
              setAppData({
              ...appData,
              language: value
            })}}>
              <SelectTrigger className="w-fit">
                <SelectValue placeholder={appData.language === "en" ? 'English' : 'Hausa'}/>
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value='en'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={UkFlag} alt="uk flag"/>
                  <span className="text-[0.875rem] text-gray  "> English </span> 
                  </span>
                </SelectItem>
                <SelectItem value='ha'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={haFlag} alt="uk flag" className="w-[14px] h-[14px]" />
                  <span className="text-[0.875rem] text-gray  "> Hausa </span> 
                  </span>
                </SelectItem>
              </SelectContent>             
            </Select>
        </div>

        {/* Form Container */}
        <div className="flex-grow flex justify-center">
          <div className="w-full relative overflow-hidden">
            <AnimatePresence mode="wait">
              {step === 1 ? (
                <motion.div
                  key="email-screen"
                  initial={{ x: "-100%", opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: "-100%", opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full"
                >
                  {/* Email Verification Screen */}
                  <div className="grid h-[70vh]">
                    <div className="grid justify-items-center">
                      <Image src={logo} alt="cafi logo" className="md:hidden" />
                      <h1 className="text-center font-fredoka font-[600] text-black-1 text-2xl">
                        {t('onboarding.hero')}
                      </h1>
                      <span className="font-blogger text-orange font-bold">{t('login.sub-title')}</span>
                      <p className="text-center hidden md:block font-medium text-gray">
                        {t('onboarding.titlelg')}
                      </p>
                      <p className="text-center font-medium md:hidden text-gray">
                        {t('onboarding.titlemd')}
                      </p>
                    </div>
                    <Form {...userVerifyForm}>
                      <form className="space-y-8 w-full">
                        <AppFormItem 
                          control={userVerifyForm.control} 
                          name="username" 
                          inputType="textInput" 
                          label="Email/Phone Number" 
                        />
                        <AppFormItem 
                          control={userVerifyForm.control} 
                          name="verificationCode" 
                          inputType="textInput" 
                          label="Verification Code" 
                        />
                  
                      </form>
                    </Form>
                    <AppButton 
                          text="Next" 
                          // onClick={handleNext} 
                          onClick={userVerifyForm.handleSubmit(onSubmitVerifyUser)}
                          className="self-end w-full"
                          isLoading={isPendingVerifyUser}
                        />
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="password-screen"
                  initial={{ x: "100%", opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: "100%", opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="w-full"
                >
                  {/* Create & Verify Password Screen */}
                  <div className="flex flex-col items-center">
                    <div className="text-center space-y-2 mb-8">
                      <h1 className="font-fredoka font-medium text-black-1 text-2xl">
                        You are almost there!
                      </h1>
                      <p className="font-medium text-gray">
                      Create password to proceed with your registration
                      </p>
                    </div>
                    <Form {...passwordForm}>
                      <form className="space-y-4 w-full">
                        <AppFormItem 
                          control={passwordForm.control} 
                          name="password" 
                          inputType="passwordInput"
                          label="Enter Password" 
                        />
                        <AppFormItem 
                          control={passwordForm.control} 
                          name="confirmPassword" 
                          inputType="passwordInput" 
                          label="Confirm Password" 
                        />
                          <AppButton 
                            text="Next" 
                            className="flex-1" 
                            isLoading={isPendingSetPassword}
                            onClick={passwordForm.handleSubmit(handleFinish)} 
                          />
                      </form>
                    </Form>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
      <div className="absolute w-full h-[35%] bottom-0 z-10">
            <Image src={farmBg} alt="farmbg" className="size-full object-cover" />
      </div>
    </div>
  );
};

export default withSplashScreen(OnboardingPage);