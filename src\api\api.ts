import axios from "axios";
import qs from "qs";
import { APP_URL } from "@/config";

interface ApiServiceInt {
  requestData?: any;
  url: string;
  method: "get" | "put" | "post" | "delete";
  protectedRoute?: boolean;
  params?: any;
  headers?: any;
  baseURL?: string;
}

/** API SUCCESSFUL RESPONSE
 *  { 
 *  "status": "success" | "error"m
 *  "message": "api description",
 *  "data" {
 *    "token": api data
 * }
 * 
 *  }
 * 
 * 
*/
/**
 * A helper function that wraps axios calls with optional:
 * - protected routes
 * - param serialization (for repeated query keys, e.g. country=xxx&country=yyy)
 */
export const apiService = async ({
  requestData,
  url,
  method,
  protectedRoute = false,
  params,
  headers,
  baseURL = APP_URL ?? "https://iam-service-dev-v25.agric-os.com",
}: ApiServiceInt) => {
  let data;
  const Axios = axios.create({
    baseURL: baseURL,
    timeout: 50000, // 50s timeout
    headers: {
      "Content-Type": "application/json",
    },
  });
  Axios.interceptors.request.use((config) => {
    if (protectedRoute) {
    
      // For example, if you store token in Redux, you can do:
      // const token = store.getState().user?.token as string;
      // config.headers.Authorization = token ? `Bearer ${token}` : '';
    }
    // Merge user-passed headers
    if (headers) {
      config.headers = { ...config.headers, ...headers };
    } 
    return config;
  });
  // Handle responses
  Axios.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      let errorMessage = "An unexpected error occurred";

      // Various error handling
      if (error.code === "ERR_NETWORK") {
        // e.g. dispatch some "no internet" state
      }
      if (
        error.code === "ERR_BAD_REQUEST" &&
        error.response?.statusText === "Unauthorized"
      ) {
        // e.g. dispatch some "not-permitted" state
      }
      if (error.response && error.response.data) {
        errorMessage = error.response.data.message || errorMessage;
      } else if (error.request) {
        errorMessage = error.message || errorMessage;
      } else {
        errorMessage = error.message || errorMessage;
      }

      if (error.response?.status === 403 && error.response.data.message.includes("not permitted")) {
        errorMessage = error;
      }

      if (error.response?.status === 401) {
        // e.g. dispatch clearing user auth, redirect to login, etc.
      }

      return Promise.reject(error);
    }
  );
  /**
   * The key: pass `paramsSerializer` so repeated params become
   * ?country=foo&country=bar, etc.
   */
  const axiosConfig = {
    params,
    headers,
    paramsSerializer: {
      serialize: (p: any) => qs.stringify(p, { arrayFormat: "repeat" }),
    },
  };
  if (method === "get") {
    const response = await Axios.get(url, axiosConfig);
    data = response.data;
  }
  if (method === "put") {
    const response = await Axios.put(url, requestData, axiosConfig);
    data = response.data;
  }
  if (method === "post") {
    // Now the requestData will be sent as JSON
    const response = await Axios.post(url, requestData, axiosConfig);
    data = response.data;
  }
  if (method === "delete") {
    // Now the requestData will be sent as JSON
    const response = await Axios.delete(url, requestData);
    data = response.data;
  }
  // Return data if shape is correct
  if (data?.data) {
    return data;
  }
  // fallback if needed
  if (data) {
    return data;
  }
  return {
    status: 400,
    message: "unsuccessful",
    data: [],
  };
};
