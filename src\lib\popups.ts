let customerLevelPopup: any = null;
export const customerLevelNewWindow = (mode:string,programId: any,id?:any) => {
  // If the popup exists and hasn't been closed, bring it to focus.
  if (customerLevelPopup && !customerLevelPopup.closed) {
    customerLevelPopup.focus();
  } else {
    // Otherwise, open a new window and store its reference.
    const url = `/popups/customer-level?programId=${programId}&mode=${mode}` + (id ? `&customerLevelId=${id}` : '');
    customerLevelPopup = window.open(
      url, 
      "customerLevelPopup", // use a unique window name
      "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200",
    );
  }
};

export function closeCustomerLevelPopup() {
  if (customerLevelPopup && !customerLevelPopup.closed) {
    customerLevelPopup.close();
    customerLevelPopup = null;
  }
}

let feedbackPopup: any = null;

export const feedbackNewWindow = (mode:string,programId: any,id?:any) => {
  if (feedbackPopup && !feedbackPopup.closed) {
    feedbackPopup.focus();
  } else {
    // Otherwise, open a new window and store its reference.
    const url = `/popups/feedback?programId=${programId}&mode=${mode}` + (id ? `&feedbackId=${id}` : '');
    feedbackPopup = window.open(
      url,
      "feedbackPopup", // use a unique window name
      "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200",
    );
  }
  }
export function closeFeedbackPopup() {
  if (feedbackPopup && !feedbackPopup.closed) {
    feedbackPopup.close();
    feedbackPopup = null;
  }
}


let assetNewWindowPopup :any = null
  export const addAssetsNewWindow = (programId:string,assetId="") => {
    // If the popup exists and hasn't been closed, bring it to focus.
    if (assetNewWindowPopup && !assetNewWindowPopup.closed) {
      assetNewWindowPopup.focus();
    } else {
      // Otherwise, open a new window and store its reference.
      if (assetId) {
        assetNewWindowPopup = window.open(
          `/popups/asset/?programId=${programId}&assetId=${assetId}`,
          "assetNewWindowPopup",
          "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200"
        )
      } else {
        assetNewWindowPopup = window.open(
          `/popups/asset/?programId=${programId}`,
          "assetNewWindowPopup",
          "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200"
        )
      }
;
    }
  };
  let activityNewWindowPopup :any = null
  export const addActivityNewWindow = (programId:string,activityId="") => {
    // If the popup exists and hasn't been closed, bring it to focus.
    if (activityNewWindowPopup && !activityNewWindowPopup.closed) {
      activityNewWindowPopup.focus();
    } else {
      // Otherwise, open a new window and store its reference.
      if (activityId) {
        activityNewWindowPopup = window.open(
          `/popups/activity/?programId=${programId}&activityId=${activityId}`,
          "activityNewWindowPopup",
          "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200"
        );
      } else {
        activityNewWindowPopup = window.open(
          `/popups/activity/?programId=${programId}`,
          "activityNewWindowPopup",
          "toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=1200,height=1200"
        );
      }
    }
  };
