# Use a smaller base image
FROM node:22-alpine AS builder

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy the rest of the application code
COPY . .

#Environmental variables
# ENV NEXT_PUBLIC_AGRIOS_WEB=$NEXT_PUBLIC_AGRIOS_WEB

# Build the application
RUN npm run build

# Use a minimal runtime base image
FROM node:22-alpine AS runtime

# Set the working directory
WORKDIR /app

#Environmental variables
# ENV NEXT_PUBLIC_AGRIOS_WEB=$NEXT_PUBLIC_AGRIOS_WEB
# ENV NEXT_PUBLIC_SSO_WEB=$NEXT_PUBLIC_SSO_WEB

# Copy only the necessary files from the builder stage
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/next.config.mjs ./

# Expose the application port
EXPOSE 3000

# Define the command to run the application
CMD ["npm", "start"]
