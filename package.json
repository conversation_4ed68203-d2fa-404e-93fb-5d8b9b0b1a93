{"name": "agricos-config", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "cy:open:local": "cypress open CYPRESS_BASE_URL=http://localhost:3000", "cy:open:dev": "cypress open CYPRESS_BASE_URL=https://portfolio-reassignment-v2.agric-os.com/", "cy:open:prod": "cypress open CYPRESS_BASE_URL=https://portfolio-reassignment.agric-os.com", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "@reduxjs/toolkit": "^2.2.6", "@tanstack/react-query": "^5.67.3", "add": "^2.0.6", "axios": "^1.7.2", "change-case": "^5.4.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "framer-motion": "^11.3.28", "i18next-browser-languagedetector": "^8.0.4", "i18next-resources-to-backend": "^1.2.1", "jwt-decode": "^4.0.0", "lenis": "^1.1.9", "next": "14.2.25", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "next-transpile-modules": "^10.0.1", "qs": "^6.14.0", "react": "^18", "react-cookie": "^8.0.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "sharp": "^0.33.4", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "yarn": "^1.22.22", "zod": "^3.24.2"}, "devDependencies": {"@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/qs": "^6.9.18", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "cypress": "^13.15.0", "eslint": "^8", "eslint-config-next": "14.2.4", "husky": "^9.1.7", "i18next": "^24.2.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "react-i18next": "^15.4.1", "react-test-renderer": "^18.3.1", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72", "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "coveragePathIgnorePatterns": ["node_modules", ".spec.ts", ".module.ts", ".dto.ts", ".enum.ts", "schema.ts", ".interface.ts", "/mocks/", "/mocks/"]}, "coverageReporters": ["lcov", "text-summary"]}