@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply box-border;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@font-face {
  font-family: "Feather";
  src: url("../assets/fonts/feather.ttf");
}

@font-face {
  font-family: "Blogger";
  src: url("../assets/fonts/blogger-sans.medium.ttf");
}


@font-face {
  font-family: "Fredoka";
  src: url("../assets/fonts/fredoka.ttf");
}

.toast-error {
  background-color: var(--toaster-error-bg) !important;
  border-color: var(--toaster-error-border) !important;
  color: var(--toaster-error-text) !important;
}

.toast-success {
  background-color: var(--toaster-success-bg) !important;
  border-color: var(--toaster-success-border) !important;
  color: var(--toaster-success-text) !important;
}