export interface RelationManagerItem {
    name?: string;
    contact_email?: string;
    contact_phone_number?: string;
  }
  
  export interface CreateOrgRequestBody {
    organization: {
      name?: string;
      short_name?: string;
      country?: string;
      address?: string;
      contact_emails?: string[];
      contact_phone_numbers?: string[];
      logo?: File | Blob | null;   // optional if you handle logo as string
      user_id?: string;
      manager_id?: string;
      org_background?: string;
    };
    relation_manager?: RelationManagerItem[];
  }