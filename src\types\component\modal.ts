export interface ConfirmActionModalProps {
    title: string;
    description: string;
    onConfirm: () => void;
    hideModal: () => void;
    tableData?: {
        key: string;
        title: string;
        value: string;
    }[]
    hasNext?: boolean;
}

export interface DoneModalProps {
    onConfirm: () => void;
    hideModal: () => void;
    title: string;
}
  
export interface EnterCommentModalProps {
    callFunc:  (comment:any) => void;
    onConfirm: () => void;
    hideModal: () => void;
    hasNext?: boolean;
}

export interface SelectIndividualModalProps {
    onConfirm: (individualName: string, individualStaffId: string) => void;
    hideModal: () => void;
    tableData: {
      name: string;
      image: string;
      ik_number: string;
      no_assigned: number
    }[]
}

export interface SelectProgramModalProps {
    onSelect: (program: {
        id: string;
        name: string;
        description?: string;
        product_type?: string;
        icon?: string;
    }) => void;
    hideModal: () => void;
    tableData: {
        id: string;
        name: string;
        description: string;
        product_type: string;
        icon?: string;
        date_created: string;
    }[];
}

export interface ApprovalStatusModalProps {
    onConfirm: () => void;
    hideModal: () => void;
   approvalData: {
    portfolioChangeType: string;
    oldRole: string;
    newRole: string;
    oldPortfolioSize: string;
    newPortfolioSize: string;
    individualsToInherit: number;
    unitApprovalStatus: string;
    hrApprovalStatus: string;
    comment: string;
   }
}

export interface TransactionHistoryModalProps {
    onConfirm: () => void;
    hideModal: () => void;
    transactionData: {
        date: string;
        portfolioChangeType: string;
        oldRole: string;
        newRole: string;
        oldPortfolioSize: string;
        newPortfolioSize: string;
        individualsToInherit: number;
        unitApprovalStatus: string;
        hrApprovalStatus: string;
        comment: string;
    }
}
export interface ReasonModalProps {
    reasons: string[];
    hideModal: () => void;
    onConfirm: (comment: string) => void;
    hasNext: boolean;
}
  