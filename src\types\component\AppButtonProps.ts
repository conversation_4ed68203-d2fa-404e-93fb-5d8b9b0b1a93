import { ReactNode } from "react";

export interface AppButtonProps {
    iconPre?: ReactNode;
    iconSuff?: ReactNode;
    text: string;
    disabled?: boolean;
    isRounded?: Boolean;
    variant?: 'default' | 'outline' | 'disable' | 'outlineDestructive' | 'destructive' | 'secondary' | 'ghost'
    onClick?: () => void,
    className?: string
    type?: "button" | "submit" | "reset";
    isLoading?: boolean,
} 

