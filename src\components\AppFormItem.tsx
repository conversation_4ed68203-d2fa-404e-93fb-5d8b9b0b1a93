"use client";
import { Control, Controller, FieldValues, Path, useFormContext } from "react-hook-form";
import { FormItem, FormLabel, FormControl, FormField, FormMessage } from "@/components/ui/form";
import { TextInput, OtpInput } from "@/components/input";

interface AppFormItemProps<T extends FieldValues> {
  placeholder?: string;
  type?: "text" | "email" | "phone" | "number";
  info?: string;
  name: Path<T>;
  inputType: "textInput" | "selectInput" | "multiSelectInput" | "uploadInput" | "passwordInput" |
  "uploadIconInput" | "toggleInput"  | "colorSelectInput" | "programSelectorInput" | "textAreaInput" | "otpInput" |
  "hierarchicalSelect" | "threeLevelDropdown" | "multiSelectTextInput" | "distributionInput" | "radioGroupInput" | "dateInput" | "iconSelectionLibrary";
  options?: any[];
  label?: string;
  control: Control<T>;
  disabled?: boolean;
  isLoading?: boolean;
  onOpenChange?: (open: boolean) => void;
  onValueChange?:any;
  error?: any;
  textTransform?: 'capitalize' | 'uppercase' | 'none';
  isDropdown?: boolean;
  pdiuValue?: number;
  pdiuUnit?: string;
  required?: boolean;
  otpLength?: number; // New prop for OTP length
  otpOnComplete?: (value: string) => void; // Callback when OTP is complete
  isNumericOnly?: boolean; 
}

const AppFormItem = <T extends FieldValues>({
  placeholder,
  type,
  name,
  label,
  control,
  inputType,
  disabled = false,
  textTransform,
  otpLength = 6, // Default OTP length of 6
  otpOnComplete,
  isNumericOnly = true,
}: AppFormItemProps<T>) => {
  
  const {
    formState: { errors }
  } = useFormContext();
  switch (inputType) {
    case 'otpInput':
      return (
        <FormField 
        name={name}
        control={control}
        render={({field}) => (
          <FormItem>
             <FormLabel className="flex items-center gap-2 text-black-2">
              <span>{label}</span>
            </FormLabel>
            <FormControl>
               <OtpInput
                name={name}
                value={field.value}
                onChange={field.onChange}
                length={otpLength}
                disabled={disabled}
                onComplete={otpOnComplete}
                isNumericOnly={isNumericOnly}
              />
              </FormControl>
          </FormItem>
        )}
        />
     
      );
    case  'passwordInput': 
      return (
        <FormField
        name={name}
        control={control}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-2 text-black-1">
              <span>{label}</span>
            </FormLabel>
            <FormControl>
              <TextInput
                      placeholder={placeholder ?? "Enter password"}
                      type="password"
                      {...field}
                      value={field.value}
                      onChange={field.onChange}
                      disabled={disabled}
                      error={!!errors[name]}
                      textTransform={textTransform}
                    />
            </FormControl>
            <FormMessage className="py-2 text-red-400 ">{errors[name]?.message as string}</FormMessage>
            </FormItem>
        )}
        />
      )
    default:
      return (
        <FormField
          name={name}
          control={control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2 text-black-1">
                <span>{label}</span>
              </FormLabel>
              <FormControl>
                {inputType === "textInput" ? (
                  <TextInput 
                    placeholder={placeholder}
                    type={type} 
                    {...field} 
                    value={field.value}
                    onChange={field.onChange}
                    disabled={disabled} 
                    error={!!errors[name]} 
                    textTransform={textTransform}
                  />
                )  : inputType === "textAreaInput" ? (
                  <TextInput 
                    placeholder={placeholder}
                    type={"textAreaInput"} 
                    {...field} 
                    value={field.value}
                    onChange={field.onChange}
                    disabled={disabled} 
                    error={!!errors[name]} 
                    textTransform={textTransform}
                  />
                ) : null}
              </FormControl>
              <FormMessage className="py-2 text-red-400">{errors[name]?.message as string}</FormMessage>
            </FormItem>
          )}
        />
      );
  }
  
};

export default AppFormItem;