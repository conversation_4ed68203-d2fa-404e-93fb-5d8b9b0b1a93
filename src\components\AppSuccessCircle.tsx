    import React from 'react';
    import { motion } from 'framer-motion';

    const PulsatingCircle = () => {
    return (
        <motion.div 
            className="relative size-40"
            animate={{
            scale: [1, 1.1, 1],
            transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
            }
            }}
        >
            {/* Outer border */}
            <motion.div 
            className="absolute inset-2 bg-primary-2 rounded-full opacity-50"
            initial={{ scale: 1 }}
            animate={{
                scale: [1, 1.3, 1],
                transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
                }
            }}
            />

            {/* 2nd border */}
            <motion.div 
            className="absolute inset-4 bg-primary-3 rounded-full opacity-70"
            initial={{ scale: 1 }}
            animate={{
                scale: [1, 1.2, 1],
                transition: {
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
                }
            }}
            />

            {/* Main circle */}
            <div className="absolute inset-6 grid items-center justify-center bg-primary rounded-full">
                <svg className='' width="43" height="34" viewBox="0 0 43 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.5022 33.8724C17.2374 33.8724 15.9726 33.338 15.0691 32.6256L1.879 21.0482C-0.289243 19.0889 -0.469935 15.8829 1.33693 13.7455C3.32449 11.6081 6.57685 11.43 8.7451 13.2111L17.9601 21.2263L33.4992 2.7024C35.3061 0.565032 38.5584 0.208801 40.9074 1.98994C43.0756 3.77108 43.437 7.15526 41.6301 9.29263L22.4773 31.9131C21.5739 32.9818 20.3091 33.6943 18.8636 33.6943C18.8636 33.8724 18.6829 33.8724 18.5022 33.8724Z" fill="white"/>
                </svg>
            </div>

        </motion.div>
    );
    };

    export default PulsatingCircle;