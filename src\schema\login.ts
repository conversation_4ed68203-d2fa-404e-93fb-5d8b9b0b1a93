import { z } from "zod";
// Helper regex patterns
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex = /^\+?\d{10,15}$/; // Accepts optional '+' and 10 to 15 digits


export const loginValidation = z.object({
    username: z.string().min(1, {
        message: "Email or Phone number is required"
      }).refine(value => {
        return emailRegex.test(value) || phoneRegex.test(value);
      }, {
        message: "Enter a valid email or phone number"
      }),
    password: z.string().min(1, {
        message: "Password is required"
    })
})

export const otpValidation = z.object({
    username: z.string().optional(),
    otp: z.string()
})

export type LoginResponseBody = z.infer<typeof loginValidation>
export type otpRequestBody = z.infer<typeof otpValidation>