import { getUserValidation, verifyUser, setPassword } from "@/api";
import { verifyUserBody } from "@/schema";
import { errorHandler } from "@/lib/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Dispatch, SetStateAction } from "react";
import { useRouter } from "next/navigation";
import { notificationToast } from "@/components/ui/sonner";
import { useLayoutContext } from "@/context";

export const useVerifyUser = (handleNext: () => void, setUserId: Dispatch<SetStateAction<string>>) => {
    return useMutation({
        mutationFn: (payload: verifyUserBody) => verifyUser(payload),
        onError: (error: unknown ) => {
                const {title, description} = errorHandler(error)
                notificationToast(title, {
                description: description,
                variant: "error"          
            })
        },
        onSuccess: (data) => {
            handleNext()
            setUserId(data?.data?.userId)
            notificationToast("User Verified successfully", {
                description: "User verification was a success",
                variant: "success"
            })
        }
    })
}

export const useGetUserValidation = (invitationId: string | null) => {
    return useQuery ({
        queryFn: () => getUserValidation(invitationId),
        queryKey: [invitationId]
    })
}

export const useSetPassword = () => {
    const { setAppData, appData } = useLayoutContext()

    const router = useRouter()
    return useMutation({
        mutationFn: (payload: {password: string, userId: string }) => setPassword({password: payload.password}, payload.userId),
        onError: (error: unknown ) => {
            const {title, description} = errorHandler(error)
            notificationToast(title, {
            description: description,
            variant: "error"            
        })
        },
        onSuccess: ({data}) => {
            setAppData({
                ...appData,
                successPageText: "Password Created Successfully"
            })
            router.push(`/success`)
        
        },
     })
}