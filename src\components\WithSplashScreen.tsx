import React, { useState, useEffect } from "react";
import farmBg from "@/assets/images/farm-bg.svg";
import Image from "next/image";
import logo from "@/assets/icons/logo.png";

// Splash screen component
const SplashMessage = () => (
  <main className="relative h-[100vh]">
    <div className="flex flex-col items-center justify-center space-y-2 h-[100vh]">
      <Image src={logo} alt="cafi logo" />
      <h1 className="text-center font-fredoka font-[600] text-black-1 text-2xl">
        Agri Finance as a Service
      </h1>
      <span className="font-blogger text-orange font-bold">Tech Ecosystem</span>
    </div>
    <div className="absolute w-full h-[35%] bottom-0 z-10">
      <Image src={farmBg} alt="farmbg" className="size-full object-cover" />
    </div>
  </main>
);

// HOC with splash screen
export default function withSplashScreen(WrappedComponent: React.ComponentType<any>) {
  function SplashScreenWrapper(props: any) {
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      simulateLoading().then(() => setLoading(false));
    }, []);

    if (loading) return <SplashMessage />;
    return <WrappedComponent {...props} />;
  }

  return SplashScreenWrapper;
}

// Simulated async load function (extracted from useEffect)
async function simulateLoading() {
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
  } catch (err) {
    // Optionally handle error
  }
}
