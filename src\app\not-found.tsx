"use client"
import { useRouter } from "next/navigation";
import { AppErrorLayout, PageWrapper }from "@/components/layout";

const NotFoundPage = () => {
  const router = useRouter();
  return (<PageWrapper>
      <AppErrorLayout 
        // icon={UnpluggedImage}
        iconAlt="Unplugged Logo"
        header="404"
        description="Page not found"
        text="Sorry we cannot find the page you are looking for"
        actionButtonText="Return to homepage"
        actionClick={() => router.push("/home")}
      />
  </PageWrapper>)
};

export default NotFoundPage;