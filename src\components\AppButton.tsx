"use client";
import React, { FC } from "react";
import clsx from "clsx";
import { Button } from "@/components/ui/button";
import { AppButtonProps } from "@/types/component";

const AppButton: FC<AppButtonProps> = ({
  text,
  variant,
  className,
  onClick,
  iconPre,
  iconSuff,
  isLoading,
  disabled,
  type = "button", // default to "button"
}) => {

  return (
    <Button
      type={type}
      className={clsx("flex w-full gap-2 rounded-[0.625rem] font-feather min-w-[10rem] h-[3.375rem]", className)}
      variant={variant}
      disabled={disabled || variant === "disable" || isLoading}
      onClick={onClick}
    >
      {iconPre && !isLoading && <span>{iconPre}</span>}
      {
        isLoading ?
        (<div className="w-10 h-10 border-4 border-t-4 border-gray-300 border-t-primary-green rounded-full animate-spin"></div>
        )
        : (<span>{text}</span>)
      }
  
      {iconSuff && !isLoading && <span>{iconSuff}</span>}
    </Button>
  );
};

export default AppButton;
