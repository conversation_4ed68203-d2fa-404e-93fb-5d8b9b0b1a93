import { z } from 'zod';
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex = /^\+?\d{10,15}$/; // Accepts optional '+' and 10 to 15 digits

export const forgotEmailValidation = z.object({
    username: z.string().min(1, {
        message: "Email or Phone number is required"
      }).refine(value => {
        return emailRegex.test(value) || phoneRegex.test(value);
    })
})

export const forgotOtpValidation = z.object({
    otp: z.string().min(6),
})

export const passwordChangeValidaton = z.object({
    otp: z.string().optional(),
    username: z.string().optional(),
    newPassword: z.string()
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character")
    .min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string(),
    }).refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
})