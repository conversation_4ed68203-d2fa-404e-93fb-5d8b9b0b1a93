---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam-gateway-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iam-gateway-app
  template:
    metadata:
      labels:
        app: iam-gateway-app
    spec:
      containers:
        - name: iam-gateway-app
          image: REGION-docker.pkg.dev/GKE_PROJECT/REPOSITORY/DEPLOYMENT_NAME:SHORT_SHA
          ports:
          - containerPort: 3000
          # ... other container configuration
