
"use client"
import { useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i8n-config';
import { useLayoutContext } from '@/context';

function TestLayout({ children} : any) {
  const { appData } = useLayoutContext()
 
  useEffect(() => {
    if (appData.language) {
      i18n.changeLanguage(appData.language);
    }
  }, [appData.language]);
  return (
<div>
<I18nextProvider i18n={i18n}>{children}</I18nextProvider></div>
  );
}

export default TestLayout;
