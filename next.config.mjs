// next.config.mjs

/** @type {import('next').NextConfig} */

const nextConfig = {
    images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: 'randomuser.me',
          pathname: '/**',
        },
        {
          protocol: 'https',
          hostname: 'storage.googleapis.com',
          pathname: '/**',
          
        },
        {
          protocol: 'https',
          hostname: '**.storage.googleapis.com', // Allow subdomains
        },
        {
          protocol: 'https',
          hostname: 'example.com',
          pathname: '/**',
        },
        {
          protocol: 'http',
          hostname: 'example.com',
          pathname: '/**',
        },
      ],
    },
  };
  
  export default nextConfig;
  