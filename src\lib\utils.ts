import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format } from "date-fns";
import axios, { AxiosError } from "axios";
import { ApiErrorResponse } from "@/types/api";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// utils/computeDiff.ts
export function computeDiff<T extends object>(originalData: T, newData: T) {
  const diff: Partial<T> = {};

  for (const key in newData) {
    if (Object.prototype.hasOwnProperty.call(newData, key)) {
      // If the values differ, or if the new value is an array that differs in length or content
      if (JSON.stringify((originalData as any)[key]) !== JSON.stringify((newData as any)[key])) {
        diff[key] = (newData as any)[key];
      }
    }
  }
  return diff;
}

export function formatDate(dateString?: string, fallback: string = "N/A"): string {
  if (!dateString) return fallback;

  try {
    const date = new Date(dateString); // Parse the ISO date string
    return format(date, "MMM dd, yyyy"); // Output: "Dec 15, 2024"
  } catch (error) {
    return fallback;
  }
}

export function capitalizeWords(sentence: string) {
  if (!sentence) return '';
  return sentence
    .split(' ') // Split the sentence into words
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize each word
    .join(' '); // Join the words back into a sentence
}

export function capitalizeFirstWord(sentence: string) {
  if (!sentence) return '';
  return sentence.charAt(0).toUpperCase() + sentence.slice(1);
}

export const errorHandler = (error: unknown) => {
  if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ApiErrorResponse>;
      switch (axiosError.response?.data.errorCode) {
          case 'INVALID_CREDENTIALS':
              return {
                  title: 'Invalid Login Error',
                  description: 'Login credential is invalid'
              }
          case 'OTP_VERIFICATION_FAILED': 
              return {
                  title: 'Otp Verification failed',
                  description: 'You have entered  the wrong otp'
              }
          case 'OTP_EXPIRED':
              return {
                  title: 'OTP Expired',
                  description: 'The otp you entered has expired. Please Login again',
              }
          case 'VERIFY_INVITATION_FAILED': 
              return {
                title: 'Unauthorized User',
                description: 'This user is not authorized'
              }
          case 'RESEND_OTP_FAILED':
              return {
                title: 'OTP is still valid',
                description: 'OTP is still valid. Please wait until it expires'
              }
          // case 'VALIDATION_FAILED':
          //   return {
          //     title: 'Validation failed',
          //     description: 'Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character'
          //   }
          default:
              return {
                  title: 'Server Error',
                  description: 'An unexpected error occurred'
              }
      }
  } else {
      return {
          title: 'Server Error',
          description: 'Application Error'
      }
  }
  
}

// Set the access token and refresh token in HTTP-only cookies
export const setAuthTokens = (accessToken: string, refreshToken: string) => {
  document.cookie = `accessToken=${accessToken}; HttpOnly; Secure; SameSite=Strict; path=/`;
  document.cookie = `refreshToken=${refreshToken}; HttpOnly; Secure; SameSite=Strict; path=/`;
};  