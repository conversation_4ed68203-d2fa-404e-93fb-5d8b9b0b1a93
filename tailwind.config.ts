import type { Config } from "tailwindcss"
import { fontFamily } from "tailwindcss/defaultTheme";

const config = {
  darkMode: ["class"],
  content: ["./pages/**/*.{ts,tsx}", "./components/**/*.{ts,tsx}", "./app/**/*.{ts,tsx}", "./src/**/*.{ts,tsx}"],
  prefix: "",
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		colors: {
  			primary: {
				DEFAULT: 'hsla(94, 97%, 40%, 1)',
				1: 'hsla(98, 68%, 26%, 1)',
				2: 'hsla(95, 100%, 40%, 0.5)',
				3: 'hsl(95.2, 66.7%, 62.4%)'
			},
			gray: {
				DEFAULT: 'hsla(242, 11%, 51%, 1)'
			},
			black: {
				1: 'hsla(245, 33%, 20%, 1)',
				2: 'hsla(207, 11%, 19%, 1)'
			},
			orange: {
				DEFAULT: 'hsla(35, 100%, 50%, 1)'
			},
			blue: {
				DEFAULT: 'hsla(199, 92%, 54%, 1)'
			}
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		},
  		width: {
  			'content': 'max-content'
  		},
		fontFamily: {
			feather: ["Feather", ...fontFamily.sans],
			blogger: ["Blogger", ...fontFamily.sans],
			fredoka: ["Fredoka", ...fontFamily.sans]
		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config