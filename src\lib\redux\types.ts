export type SidebarState = {
  isSidebarOpen: boolean;
  isRightSidebarOpen: boolean;
};

export type LanguageSelectorState = {
  language: string;
};

// File: /types/state/redux.ts

export interface OrganizationFormData {
  name: string;
  short_name: string;
  country: string;
  address: string;
  contact_emails: string[];
  contact_phone_numbers: string[];
  logo: File | null;  // or string if you still store base64
  relation_manager: Array<{
    name: string;
    contact_email: string;
    contact_phone_number: string;
  }>;
}

export interface CountryOperationFormData {
  name: string;
  short_name: string;
  organization: string;
  country: string;
  address: string;
  contact_emails: string[];
  contact_phone_numbers: string[];
  logo: File | null;  // or string if you still store base64
}

