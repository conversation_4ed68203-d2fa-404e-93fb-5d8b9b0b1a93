import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query"
import { passwordReset, passwordResetRequest } from "@/api/forgotPassword"
import { notificationToast } from "@/components/ui/sonner"
import { errorHandler } from "@/lib/utils"

export const usePasswordResetRequest = (handleNext: () => void) => {
    return useMutation({
        mutationFn: (payload: { username: string }) => passwordResetRequest(payload),
        onError: (error: unknown) => {
            const { title, description } = errorHandler(error)
            notificationToast(title, {
                description: description,
                variant: 'error'
            })
        },
        onSuccess: () => {
            handleNext(),
            notificationToast('Password Reset Request Successfull', {
                description: "Token has been sent to you email",
                variant: "success"
            })
        }
    })
}


export const usePasswordReset = () => {
    const router = useRouter()
    return useMutation({
        mutationFn: (payload: {
            token: string;
            username: string;
            newPassword: string;}
        ) => passwordReset(payload),
        onError: (error: unknown) => {
            const { title, description } = errorHandler(error)
            notificationToast(title, {
                description: description,
                variant: 'error'
            })
        },
        onSuccess: () => {
            notificationToast('Password Reset Successfull', {
                description: "Your password has been reset successfully",
                variant: "success"
            })
            router.push('/success?type=resetPassword')
        }
    })
}