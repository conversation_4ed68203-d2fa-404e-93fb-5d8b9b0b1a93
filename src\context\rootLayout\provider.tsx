import {
  createContext,
  useContext,
  Dispatch,
  SetStateAction,
  ReactNode,
  useState,
  useMemo
} from "react";
import { useCookies } from "react-cookie";

interface IappData {
  accessToken: string;
  refreshToken: string;
  callBackUrl?: string;
  language?: string;
  mobileAppPackage?: string;
  mobileAppScheme?: string;
  successPageText?: string;
  loginReason?: string;
}

type LayoutContextType = {
  appData: IappData;
  setAppData: Dispatch<SetStateAction<IappData>>;
};

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

const useLayoutContext = () => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error("useLayoutContext must be used within a LayoutProvider");
  }
  return context;
};

type LayoutProviderProps = {
  children: ReactNode;
};

export const LayoutProvider = ({ children }: LayoutProviderProps) => {
  const [cookies] = useCookies(["accessToken", "refreshToken"]);

  const [appData, setAppData] = useState<IappData>({
    accessToken: cookies.accessToken || "",
    refreshToken: cookies.refreshToken || "",
    callBackUrl: "",
    language: "en",
  });

  const contextValue = useMemo(() => ({ appData, setAppData }), [appData]);

  return (
    <LayoutContext.Provider value={contextValue}>
      {children}
    </LayoutContext.Provider>
  );
};

export { LayoutContext, useLayoutContext };
