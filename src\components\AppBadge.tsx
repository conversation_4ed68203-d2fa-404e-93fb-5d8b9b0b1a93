"use client";
import { FC } from "react";
import Image from "next/image";

// Example icons
// import { GreenMarkIcon, PendingIcon, InfoIcon } from "@/assets/icons"; 
// ^ InfoIcon can be a fallback if needed

interface AppBadgeProps {
  /**
   * The label displayed in the badge. 
   * e.g. "Active", "Inactive", "Enabled", "Disabled", etc.
   */
  label: string;

  /**
   * A value used internally to determine default style/icon. 
   * Usually the same as 'label', but can be different if you prefer.
   */
  value?: string;

  /**
   * If you want a custom icon, pass its URL or import a local svg and do `icon: MyIcon`.
   */
  icon?: string;

  /**
   * Custom background + text color classes. 
   * e.g. "bg-green-100 text-green-700"
   */
  bgColor?: string;
}

/**
 * A map of known statuses -> default styles/icons.
 * Extend this with as many statuses as you need.
 */
const STATUS_MAP: Record<string, { bgClass: string; icon?: string }> = {
  enabled: {
    bgClass: "bg-primary-green2",
    // icon: GreenMarkIcon,
  },
  disabled: {
    bgClass: "bg-secondary-yellow3",
    // icon: PendingIcon,
  },
  active: {
    bgClass: "bg-primary-green2",
    // icon: GreenMarkIcon,
  },
  inactive: {
    bgClass: "bg-secondary-yellow3",
    // icon: PendingIcon,
  },
  // add more if needed, e.g. "transferred", "pending", etc.
};

/**
 * AppBadge: A reusable badge for statuses.
 */
const AppBadge: FC<AppBadgeProps> = ({ label, value, icon, bgColor }) => {
  const statusKey = (value ?? label)?.toLowerCase();

  // Look up defaults from the STATUS_MAP
  const found = STATUS_MAP[statusKey];

  // If found, use that background color; else fallback 
  const defaultBgClass = found?.bgClass || "bg-gray-200 text-primary-forestGreen";
  // If found, use that icon; else fallback

  return (
    <div
      className={`inline-flex items-center gap-1 py-1 px-2 rounded-lg text-primary-forestGreen text-sm
        ${bgColor ?? defaultBgClass} w-max`}
    >
      <Image
        alt={`${statusKey}-icon`}
        src={icon ?? ''}
        width={16}
        height={16}
      />
      <span>
        {label}
      </span>
    </div>
  );
};

export default AppBadge;
