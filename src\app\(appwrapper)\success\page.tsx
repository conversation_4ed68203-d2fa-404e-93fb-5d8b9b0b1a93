"use client"
import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
    AppButton,
    AppSuccessCircle
} from "@/components"
import { useDeviceDetection } from "@/hooks";
import { useLayoutContext } from "@/context";


const SuccessPage = () => {
    const router = useRouter()
    const device = useDeviceDetection()
    const { appData } = useLayoutContext()
    const searchParams = useSearchParams();
    const callBackUrl = searchParams.get('redirect_uri')
    const {
        accessToken,
        refreshToken,
        successPageText,
        mobileAppScheme,
        mobileAppPackage,
        loginReason
    } = appData
    useEffect(() => {
        if (callBackUrl  && callBackUrl !== "null" && callBackUrl !== ""&& device === 'Desktop') {
            const urlWithToken = callBackUrl.includes('?') 
                ? `${callBackUrl}&accessToken=${accessToken}&refreshToken=${refreshToken}`
                : `${callBackUrl}?accessToken=${accessToken}&refreshToken=${refreshToken}`;
            router.push(urlWithToken);
            }
    }, [device, appData])

    return (
        <div className="container mx-auto max-w-[90%] grid items-center justify-center h-[100vh]">
            <div className='flex flex-col items-center gap-2 justify-center'>
                <AppSuccessCircle />
                <div className='font-fredoka text-2xl text-center font-semibold'>
                    {successPageText}
                </div>
            </div>
            {
                !(callBackUrl  && callBackUrl !== "null" && callBackUrl !== "") && mobileAppScheme && mobileAppPackage && !loginReason && (
                    <a href={`intent://deeplink/success#Intent;scheme=${mobileAppScheme};package=${mobileAppPackage};S.at=${accessToken}];S.rt=${refreshToken};S.rp=false;end;`} target="_blank">
                        <AppButton text="Open App" />
                    </a>
                )
            }
   
            {
              !(callBackUrl  && callBackUrl !== "null" && callBackUrl !== "") && mobileAppScheme && mobileAppPackage && loginReason === 'resetPin' && (
                    <a href={`intent://deeplink/success#Intent;scheme=${mobileAppScheme};package=${mobileAppPackage};S.at=${accessToken}];S.rt=${refreshToken};S.rp=true;end;`} target="_blank">
                        <AppButton text="Open App" />
                    </a>
                )
            }
        </div>
    )
}

export default SuccessPage