import * as React from "react"

import { cn } from "@/lib/utils"

interface InputProps extends React.ComponentProps<"input"> {
  error?: boolean
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-[3rem] w-full rounded-[0.625rem] border px-3 py-1 text-base shadow-sm transition-colors placeholder:text-gray focus-visible:outline-none",
          // Default state classes.
          "border-gray bg-gray/10 text-accent-body focus:bg-white",
          // When disabled, keep appearance similar (as per previous discussion).
          "disabled:opacity-100 disabled:cursor-default disabled:rounded-none disabled:bg-transparent",
          // Conditionally apply error styling.
          error ? "border-red-500" : "",
          // any additional classes from className prop
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)

Input.displayName = "Input"

export { Input }