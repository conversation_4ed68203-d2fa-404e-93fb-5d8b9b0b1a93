import type { TopBarProps } from "@/types/component";
import { type PropsWithChildren, ReactNode } from "react";
import { cn } from "@/lib/utils";

/**
 * PageWrapper
 * - Wraps page contents with a top bar and left sidebar-aware layout.
 * - (Now) optionally includes a right sidebar if `rightBar` is given.
 * - Renders modals without interfering with page layout.
 */

interface PageWrapperProps extends PropsWithChildren<TopBarProps> {
  className?: string;
  isError?: boolean;
  isLoading?: boolean;
  hasSidebar?: boolean;
  /** 
   * If you want a right sidebar, pass the node here (your own component).
   * Could also add `showRightBar?: boolean;` if you want a toggle.
   */
  rightBar?: ReactNode;  // <--- new prop
}

const PageWrapper = ({
  children,
  className,
  showSearch = true,
  showFilter = true,
  searchPlaceholder = "",
  handleSearch,
  Filter,
  showExport = false,
  selectedIds,
  isExporting = false,
  isError = false,
  isLoading,
  handleExport,
  filterGroups,
  statusMaps,
  countryMap,
  regionMap,
  zoneMap,
  productTypeMap,
  loading,
  headerRight,
  rightBar,          // <--- new prop
  hasSidebar,

}: PageWrapperProps) => {


  return (
    <>

      {/* MAIN CONTENT WRAPPER */}
      <main
      >
        
        {/* FLEX CONTAINER FOR PAGE CONTENT + OPTIONAL RIGHT SIDEBAR */}
        <div className="flex min-h-screen pt-20">
          {/* MAIN CONTENT AREA */}
          <div className={cn("flex-1 bg-[#F2F2F4] overflow-hidden px-6", className)}>

          </div>

          {/* RIGHT SIDEBAR (if provided) */}
          {rightBar && !isError && (
            <aside
              className={cn(
                "border-l border-gray-200 bg-white",
                " w-[20vw] px-4"
              )}
            >
              {rightBar}
            </aside>
          )}
        </div>
      </main>
    </>
  );
};

export default PageWrapper;
