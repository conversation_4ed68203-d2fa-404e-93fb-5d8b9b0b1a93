"use client";
import React, { useRef, KeyboardEvent, ClipboardEvent } from "react";
import { Control, FieldValues, Path } from "react-hook-form";

interface OtpInputProps<T extends FieldValues> {
  name: Path<T>;
  value: string;
  length?: number;
  disabled?: boolean;
  onComplete?: (value: string) => void;
  isNumericOnly?: boolean;
  onChange: (...event: any[]) => void; 
}

export const OtpInput = <T extends FieldValues>({
  name,
  value,
  onChange,
  length = 6,
  disabled = false,
  onComplete,
  isNumericOnly = true,
}: OtpInputProps<T>) => {
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
    onChange: (...event: any[]) => void,
    value: string
  ) => {
    const val = e.target.value;
    // If numericOnly is true, only accept numbers
    if (isNumericOnly && val && !/^\d+$/.test(val)) {
      return;
    }
    // Only take the last character if multiple characters are entered
    const digit = val.slice(-1);
    // Create a new array from current value
    const newValue = value ? value.split("") : Array(length).fill("");
    newValue[index] = digit;
    // Update the form value
    onChange(newValue.join(""));
    // Move focus to next input if we entered a value
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
    // Check if OTP is complete and call onComplete callback
    if (newValue.filter(Boolean).length === length && onComplete) {
      onComplete(newValue.join(""));
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    // Handle backspace to move to previous input
    if (e.key === "Backspace" && index > 0 && !e.currentTarget.value) {
      inputRefs.current[index - 1]?.focus();
    }
    
    // Arrow navigation between inputs
    if (e.key === "ArrowLeft" && index > 0) {
      e.preventDefault();
      inputRefs.current[index - 1]?.focus();
    }
    
    if (e.key === "ArrowRight" && index < length - 1) {
      e.preventDefault();
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handlePaste = (
    e: ClipboardEvent<HTMLInputElement>,
    onChange: (...event: any[]) => void
  ) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();
    
    // Only process if we have data
    if (!pastedData) return;
    
    // Only accept numbers if isNumericOnly is true
    if (isNumericOnly && !/^\d+$/.test(pastedData)) return;
    
    // Take only up to the specified length
    const pastedChars = pastedData.slice(0, length).split("");
    
    // Create the new value
    const newValue = Array(length).fill("");
    pastedChars.forEach((char, idx) => {
      if (idx < length) newValue[idx] = char;
    });
    
    // Update the form value
    onChange(newValue.join(""));
    
    // Focus the next empty input or the last input
    const nextEmptyIndex = newValue.findIndex(val => !val);
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus();
    } else {
      inputRefs.current[length - 1]?.focus();
    }
    
    // Call onComplete if OTP is complete
    if (newValue.filter(Boolean).length === length && onComplete) {
      onComplete(newValue.join(""));
    }
  };

  return (

            <div className="flex gap-2 items-center justify-center">
              {Array.from({ length }).map((_, index) => (
                <input
                  key={`otp-input-${index}`}
                  type="text"
                  name={name}
                  maxLength={1}
                  ref={(el) => {
                    // Store ref without returning anything
                    inputRefs.current[index] = el;
                  }}
                  value={value?.[index] || ""}
                  onChange={e => handleChange(e, index, onChange, value)}
                  onKeyDown={e => handleKeyDown(e, index)}
                  onPaste={e => handlePaste(e, onChange)}
                  disabled={disabled}
                  className="w-12 h-12 text-center text-lg border border-primary-gray  bg-gray/10 rounded-md focus:border-primary focus:ring-1 focus:bg-white focus:ring-primary outline-none"
                  autoComplete="one-time-code"
                  inputMode={isNumericOnly ? "numeric" : "text"}
                />
              ))}
            </div>
  );
};
