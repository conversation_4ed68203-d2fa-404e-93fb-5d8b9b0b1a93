import { useState, useEffect } from 'react';

export const useDeviceDetection = () => {
    const [ device, setDevice ] = useState('');

    useEffect(() => {
        const handleDeviceDection = () => {
            const userAgent = navigator.userAgent.toLowerCase();
            const isMobile = /iphone|ipad|ipod|android|blackberry|windows phone/g.test(userAgent)
            const isTablet = /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/g.test(userAgent)

            if (isMobile) {
                setDevice('Mobile')
            } else if (isTablet) {
                setDevice('Tablet')
            } else {
                setDevice('Desktop')
            }
        };
        handleDeviceDection()
        window.addEventListener('resize', handleDeviceDection);

        return () => {
            window.removeEventListener('resize', handleDeviceDection);
        }
    }, [])

    return device
}


