// i18n-config.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import en from '@/assets/lang/en.json';
import ha from '@/assets/lang/ha.json';
import yor from '@/assets/lang/yor.json';
import igb from '@/assets/lang/igb.json';

if (!i18n.isInitialized) {
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources: {
        en: { translation: en },
        ha: { translation: ha },
        yor: { translation: yor },
        igb: { translation: igb },
      },
      fallbackLng: 'en',
      interpolation: { escapeValue: false },
    });
}

export default i18n;
