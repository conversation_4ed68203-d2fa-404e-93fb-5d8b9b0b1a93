import { apiService } from "./api";
import {
  LoginResponseBody,
  otpRequestBody
} from "@/schema";
import {
  LoginResponse,
  OtpResponse
} from "@/types/api";

export const login = async (payload: LoginResponseBody) : Promise<LoginResponse> => {
    return await apiService({
      url: `/auth/login`,
      method: "post",
      protectedRoute: false,
      requestData: payload,
    });
}

export const verifyOtp = async (payload: otpRequestBody): Promise<OtpResponse> => {
  return await apiService({
    url: 'auth/otp/verify',
    method: 'post',
    protectedRoute: false,
    requestData: payload
  })
}

export const resendOtp = async (payload: { username: string}) : Promise<{
  status: "success" | "error";
  message: string;
  data: string
}> => {
  return await apiService({
    url: `auth/otp/resend`,
    method: "post",
    protectedRoute: false,
    requestData: payload,
  });
}
