import { useCallback, useEffect, useState } from "react"
import AppButton from "./AppButton";

const OtpTimer = ({
  initialSeconds = 10,
    isActive,
    // setIsActive,
    isLoading,
    onResend,
    onExpire
}: {
    initialSeconds: number;
    isActive: boolean,
    isLoading: boolean,
    onResend: () => void;
    onExpire: () => void;
}) => {
  const [isTimerActive, setIsTimerActive] = useState(isActive);
  const [ seconds, setSeconds ] = useState(initialSeconds)
  
  const formatTime = useCallback((totalSeconds: number) => {
      const minutes = Math.floor(totalSeconds / 60);
      const remainingSeconds = totalSeconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  const resetTimer = useCallback(() => {
    setSeconds(initialSeconds);
    onResend()
    setIsTimerActive(true);
  }, [initialSeconds, onResend]);

  useEffect(() => {
    let interval: any;
    if (isActive && seconds > 0) {
      interval = setInterval(() => {
        setSeconds(prevSeconds => prevSeconds - 1);
      }, 1000);
    } else if (seconds === 0) {
      setIsTimerActive(false)
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [isActive, seconds, onExpire]);

  return (
    <div className="grid justify-center items-center gap-2">
        <div className="text-center  text-gray"> Code Expires in </div>
        <div className="text-center"> {formatTime(seconds)} </div>
        <AppButton 
            text="Resend Code"
            variant="outline"
            disabled={isTimerActive}
            isLoading={isLoading}
            onClick={() => {
              resetTimer()
            }}
        />
    </div>

  )
}

export default OtpTimer