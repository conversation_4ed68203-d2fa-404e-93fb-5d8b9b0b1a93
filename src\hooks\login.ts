import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { login, resendOtp, verifyOtp } from "@/api";
import {
    LoginResponseBody,
    otpRequestBody
} from "@/schema";
import { notificationToast } from "@/components/ui/sonner";
import { useLayoutContext } from "@/context";
import { errorHandler } from "@/lib/utils";
import { IauthCookieValue } from "@/types/api";
import { useCookies } from "react-cookie";

export const useLogin = (handleNext: () => void) => {
    return  useMutation({
        mutationFn: (payload: LoginResponseBody) => login(payload),
        onError: (error: unknown ) => {
            const {title, description} = errorHandler(error)
             notificationToast(title, {
                description: description,
                variant: 'error'
            })
        },
        onSuccess: () => {
            handleNext() 
            notificationToast("Login Successful", {
                description: "Login Attempt was a success",
                variant: "success"
            })
        }
    })
}
export const useOtpVerify = () => {
    const { setAppData, appData } = useLayoutContext()
    const [cookies, setCookies] = useCookies<string, IauthCookieValue>(['accessToken', 'refreshToken']); 
    const router = useRouter()
    return  useMutation({
        mutationFn: ( payload:otpRequestBody) => verifyOtp(payload),
        onSuccess: ({data}) => {
            setAppData({
                ...appData,
                accessToken: data.accessToken,
                refreshToken: data.refreshToken,
                successPageText: 'Login Successful!!'
            })
            setCookies('refreshToken', data.refreshToken, {
                sameSite: "strict",
            })
            setCookies('accessToken', data.accessToken, {
                sameSite: "strict"
            })
            if (appData.callBackUrl) {
             router.push(`/success?redirect_uri=${appData.callBackUrl}`)    
            } else {
                router.push('/success')
            }
        },
        onError: (error) => {
            const {title, description} = errorHandler(error)
            notificationToast(title, {
            description: description,
            variant: "error"
        })
    },
    })
}
export const useOtpResend = () => {
    return useMutation({
        mutationFn: (payload: { username: string }) => resendOtp(payload),
        onSuccess: () => {
            notificationToast('Otp token resent', {
                description: 'The Otp token has been sent successfully',
                variant: "success"
            })
        },
        onError: (error) => {
            const {title, description} = errorHandler(error)
            notificationToast(title, {
            description: description,
            variant: "error"
            })
        },
    })
}