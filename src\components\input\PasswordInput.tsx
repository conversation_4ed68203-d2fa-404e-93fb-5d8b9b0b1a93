"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";

interface PasswordInputProps {
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  disabled?: boolean;
  error?: boolean;
  textTransform?: 'capitalize' | 'uppercase' | 'none';
  className?: string;
}

const PasswordInput = ({
  placeholder = "Enter password",
  value,
  onChange,
  onBlur,
  disabled = false,
  error = false,
  textTransform,
  className = "",
}: PasswordInputProps) => {
  const textTransformClass =
  textTransform === "capitalize"
    ? "capitalize"
    : textTransform === "uppercase"
    ? "uppercase"
    : "";


  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="relative">
      <Input
        type={showPassword ? "text" : "password"}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        disabled={disabled}
        className={`pr-10 ${error ? "border-red-500" : ""} ${textTransformClass} ${className}`}
      />
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
        tabIndex={-1}
      >
        {showPassword ? (
        //   <EyeOff size={18} />
        <div> open </div>
        ) : (
        //   <Eye size={18} />
        <div> close </div>
        )}
      </button>
    </div>
  );
};

export default PasswordInput;