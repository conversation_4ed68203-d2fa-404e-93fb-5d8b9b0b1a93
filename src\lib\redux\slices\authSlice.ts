import { DEFAULT_USER_CONFIGS } from "@/lib/constants";
import type { UserConfigs } from "@/types/state/auth";
import type { AuthState } from "@/types/state/redux";
import { createSlice } from "@reduxjs/toolkit";


type ManagedOrganization = {
  name: string;
  logo?: string;
  organizationId: string | number;
  organizationShortName: string;
};

const initialState: AuthState = {
  user: {
    token: "",
    configs: DEFAULT_USER_CONFIGS,
    managedOrganization: {
      name: "",
      logo: "",
      organizationId: "",
      organizationShortName: "",
    },
  },
  isAccessDenied: false,
  isSessionExpired: false,
  isSomethingWrong: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    updateUserToken: (state, { payload }: { payload: string }) => {
      state.user.token = payload;
    },
    updateUserConfigs: (state, { payload }: { payload: UserConfigs }) => {
      state.user.configs = payload;
    },
    updateAccessDenied: (state, { payload }: { payload: boolean }) => {
      state.isAccessDenied = payload;
    },
    updateManagedOrganization: (state, { payload }: { payload: ManagedOrganization }) => {
      state.user.managedOrganization = payload;
    },
    updateSessionExpired: (state, { payload }: { payload: boolean }) => {
      state.isSessionExpired = payload;
    },
    updateSomethingWrong: (state, { payload }: { payload: boolean }) => {
      state.isSomethingWrong = payload;
    },
    // logOutUser: () => {
    //   localStorage.clear();
    //   window.location.replace(`${SSO_WEB}/logout/?appUrl=${window.location.origin}`);
    // },
    // reLogIn: () => {
    //   localStorage.clear();
    //   const location = window.location.href.endsWith("/") ? window.location.href.slice(0, -1) : window.location.href;
    //   window.location.replace(`${SSO_WEB}/logout/?appUrl=${location}`);
    // },
  },
});

export const { updateUserToken, updateUserConfigs, updateAccessDenied, updateSessionExpired,updateManagedOrganization, updateSomethingWrong } =
  authSlice.actions;

export default authSlice.reducer;
