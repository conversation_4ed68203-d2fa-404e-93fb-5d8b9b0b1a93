import type { UserConfigs } from "@/types/state/auth";
import { type StaticImageData } from "next/image";

export const LANGUAGES: { id:number, name: "Hausa" | "English" }[] = [
  {
    id:1,
    name: "Hausa",
  },
  {
    id:2,
    name: "English",
  },
];

export const DEFAULT_USER_CONFIGS: UserConfigs = {
  canApprove: false,
};

export const PRIMARY_COLOUR_GREEN = "#59C903";
export const PRIMARY_COLOUR_ORANGE = "#FF9600";
export const PRIMARY_COLOUR_YELLOW = "#F8DF3F";
export const PRIMARY_COLOUR_RED = "#FF4B4B";
export const PAGE_SIZES = [20, 50, 100, 500, 1000];

export const REQUESTS_FILTER_GROUPS = [
  {
    title: "Hub",
    values: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>_Ch<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
  },
  {
    title: "Status",
    values: ["Approved","Pending","Part Paid"],
  },
];

export const BABBAN_GONA_IMAGE_BUCKET = "https://storage.googleapis.com/babbangona-prod-bucket-2";

