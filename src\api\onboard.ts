import { apiService } from "./api";
import { verifyUserBody } from "@/schema";

export const verifyUser = async (payload: verifyUserBody) => {
    return await apiService({
      url: `invitations/verify`,
      method: "post",
      protectedRoute: false,
      requestData: payload,
    });
}

export const getUserValidation = async (invitationId: string | null) => {
    return await apiService({
        url: `invitations/${invitationId}/validate`,
        method: "get",
        protectedRoute: false
    })
}

export const setPassword = async (payload: {password: string}, userId: string, ) => {
    return await apiService({
        url: `users/${userId}/password`,
        method: 'post',
        protectedRoute: false,
        requestData: payload
    })
}