export interface LoginResponseBody {
    username: string;
    password: string;
}

export interface LoginResponse {
    status: "success" | "error";
    message: string;
    data: {
        "token": string
    }
}

export interface OtpResponse {
    status: "success" | "error";
    message: string;
    data: {
        accessToken: string,
        refreshToken: string
    }
}

export interface IauthCookieValue {
    accessToken?: string;
    refreshToken?: string;
}