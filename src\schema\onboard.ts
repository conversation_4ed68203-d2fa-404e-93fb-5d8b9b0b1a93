import { z } from "zod";

// Helper regex patterns
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const phoneRegex = /^\+?\d{10,15}$/; // Accepts optional '+' and 10 to 15 digits

export const verifyUserValidation = z.object({
  username: z.string().min(1, {
    message: "Email or Phone number is required"
  }).refine(value => {
    return emailRegex.test(value) || phoneRegex.test(value);
  }, {
    message: "Enter a valid email or phone number"
  }),
    verificationCode: z.string().min(1, {
      message: "Verification code is required"
    })
})

export const setPasswordValidation = z.object({
  password: z.string()
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character")
    .min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export type verifyUserBody = z.infer<typeof verifyUserValidation>
export type setPasswordBody = z.infer<typeof setPasswordValidation>