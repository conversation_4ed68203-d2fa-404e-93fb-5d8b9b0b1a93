# Agrios-web

Web portal for Agrios configuration app.

## Resources

* [User Interface](https://www.figma.com/design/gwDIJoCIAzwE8v70oRSR00/CAFI-Tech-Ecosystem%3A-Mobile-Platform?node-id=1036-582&t=XiXke6L7vcPgyroy-0)
* [User Interface 2] (https://www.figma.com/design/3nroU8B67JMLzahgN1HepH/AFAAS-POULTRY?node-id=1-7&t=NnCAmDWBZNHiEqge-1)
* [Project Board](https://babbangona.atlassian.net/jira/software/projects/AIAG/boards/276)
* [User Workflow](https://www.figma.com/board/V5zOML0DpZU6T6UP0ySJYY/IAM-Service?node-id=0-1&t=zbB0kr9BEPpopJnj-1)
* [Dev live link](https://iam-service-frontend-v25.agric-os.com)
* [Staging live link](https://iam-service-frontend-staging-v25.agric-os.com/)
* [Backend Staging link](https://iam-service-staging-v25.agric-os.com)
* [Backend Dev link](https://iam-service-dev-v25.agric-os.com)
* [Backend Postman](https://galactic-resonance-793427.postman.co/workspace/CAFI-TECH-ECOSYSTEM~e90be2d0-29c2-4dae-bd0c-270f7f01ad68/collection/26636754-c11a86bc-af0a-4d93-af90-3ca8ebd7be15?action=share&creator=26636754)
## Prerequisites

Before starting, ensure you have the following installed on your local machine:

- **Node.js** (version 14.x or higher)  
- **Yarn** (package manager)  

---

## Design Library Installation

To install the design library used by this project, run the following command:

```bash
yarn add https://github.com/BabbanGonaDev/agricos25-design-library#dev
```

---

## Local Development Setup

Follow these steps to start the application locally:

1. **Clone the Repository**  
   Clone the repository to your local machine:  
   ```bash
   git clone https://github.com/BabbanGonaDev/agricos25-iam-gateway-app
   cd agrios-web
   ```

2. **Install Dependencies**  
   Use Yarn to install the required dependencies:  
   ```bash
   yarn install
   ```

3. **Start the Development Server**  
   Run the following command to start the development server:  
   ```bash
   yarn dev
   ```

4. **Access the Application**  
   Open your browser and navigate to:  
   ```plaintext
   http://localhost:3000
   ```

---
## Commit Message Guidelines

When making commits, follow the conventional commit format. The type must be one of the following:  

```plaintext
[build, chore, ci, docs, feat, fix, perf, refactor, revert, style, test]
```

### Example Commit Message
```plaintext
feat: add new user authentication flow
```


### Authentication Redirect Flow – Integration Guide

This document outlines how mobile and web applications should pass redirect URIs to the IAM service, and how the authentication service responds with access and refresh tokens after a successful login.

## 📱Mobile Applications

Follow these steps to integrate the mobile application:

1. **Initiate Authentication**  
   Mobile apps must initiate the login flow by passing the mobile_redirect_uri as a query parameter to the IAM login URL.  
   ```bash
   https://iam-service-frontend-v25.agric-os.com?scheme=yourapp&package=com.farmbase.app
   ```
   - *scheme*: This is your application name
   - *package*: This is the android package name

2. **Redirect After Successful Login**
   After authentication, the IAM service redirects to the mobile_redirect_uri with access and refresh tokens embedded in the intent URI.
   ```bash 
   intent://deeplink/success#Intent;scheme=yourapp;package=com.farmbase.app;S.accessToken=abc123;S.refreshToken=xyz789;end;
   ```
   - *S.accessToken*: Access token issued by the IAM service.
   - *S.refreshToken*: Refresh token for session renewal.

## 💻 Web Applications
1. **Initiate Authentication**  
   Web applications must pass the redirect_uri as a query parameter.
   ```bash
      https://iam-service-frontend-v25.agric-os.com?redirect_uri=https://frontend.com
   ```
   - *redirect_uri*: This is a query parameter which conatins the web applications callback url

2. **Redirect After Successful Login**  
   After successful login, the IAM service redirects to the given redirect_uri, appending the tokens as query parameters.
   - If the URL has no existing query parameters:
   ```bash
      https://frontend.com?accessToken=abc123&refreshToken=xyz789
   ```
   
   -  If the URL already has query parameters:
   ```bash 
      https://frontend.com?foo=bar&accessToken=abc123&refreshToken=xyz789
   ```