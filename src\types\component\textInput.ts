import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FocusEventHandler } from "react";

export interface TextInputProps {
    disabled?: boolean;
    value?: string;
    className?: string;
    placeholder?: string;
    type?: "text" | "email" | "phone" | "textAreaInput" | "number" | "password";
    onChange?: ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>; 
    onBlur?:  FocusEventHandler<HTMLInputElement | HTMLTextAreaElement>;
    error?: boolean;
    textTransform?: 'capitalize' | 'uppercase' | 'none';
}