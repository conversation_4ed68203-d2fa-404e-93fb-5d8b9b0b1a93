"use client"
import { FC } from "react"
import Image from "next/image";
import { AppButton } from "@/components/"
import { AppErrorLayoutInt } from "@/types/component";

const AppErrorLayout: FC<AppErrorLayoutInt> = ({ icon, header, description, text, actionButtonText, actionClick, cancelButtonText, cancelClick}) => {
    return(
        <div className="flex min-h-[70vh] overflow-hidden flex-col items-center justify-center bg-white"> 
        {icon && <Image src={icon} alt="Unplugged Logo" width={500} height={500} className="mb-4 h-[60px] w-[60px]" /> }
        <div className="flex flex-col gap-3 text-center">
          <p className={`font-700 text-[2.5rem] font-feather`}>{header}</p>
          <p className={`font-700 text-[1.25rem] font-feather opacity-60`}>{description}</p>
          <p className="text-[1.25rem]">{text}</p>
          <div className="flex space-x-4">
            {cancelButtonText && (
                <AppButton 
                className={`font-700 font-feather mt-4 h-[54px] w-full`}
                onClick={cancelClick}
                variant="outline"
                text={cancelButtonText}
            />
            )}
            <AppButton 
                className={`font-700 font-feather mt-4 h-[54px] w-full`}
                onClick={actionClick}
                text={actionButtonText}
            />
          </div>
        </div>
      </div>
    )
}


export default AppErrorLayout