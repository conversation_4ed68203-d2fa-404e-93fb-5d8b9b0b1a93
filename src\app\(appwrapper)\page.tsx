"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form";
import { jwtDecode } from "jwt-decode";
import { zodResolver } from "@hookform/resolvers/zod"; 
import { Form } from "@/components/ui/form";
import { AppButton, AppFormItem, OtpTimer, withSplashScreen } from "@/components"; 
import logo from "@/assets/icons/logo.png"
import UkFlag from "@/assets/icons/uk-flag.svg"
import roundLeft from "@/assets/icons/round-left.png"
import haFlag from "@/assets/images/hausa-logo.png"
import farmBg from "@/assets/images/farm-bg.svg"
import { loginValidation, LoginResponseBody, otpValidation } from "@/schema";
import { useLogin, useOtpResend, useOtpVerify } from "@/hooks";
import { AnimatePresence, motion } from "framer-motion";
import { useLayoutContext } from "@/context";
import { IauthCookieValue } from "@/types/api";
import { useCookies } from "react-cookie";


const EntryPoint = () => {
  const [ step, setStep] = useState(1)
  const { setAppData, appData } = useLayoutContext()
  const { t, i18n } = useTranslation();
  const { mutate: mutateOtpVerify, isPending: isPendingOtpVerify } = useOtpVerify()
  const { mutate: mutateOtpResend, isPending: isPendingOtpResend } = useOtpResend()
  const searchParams = useSearchParams();
  const callBackUrl = decodeURI(searchParams.get('redirect_uri') as string)
  const mobileAppScheme = searchParams.get('scheme')
  const mobileAppPackage = searchParams.get('package')
  const loginReason = searchParams.get('reason')
  const [cookies] = useCookies<string, IauthCookieValue>(['accessToken', 'refreshToken']); 
  const form = useForm({
    resolver: zodResolver(loginValidation),
    defaultValues: {
        username: "",
        password: ""
    }
  })
  const otpForm = useForm({
    resolver: zodResolver(otpValidation),
    defaultValues: {
      username: "",
      otp: ""
    }
  })
  const username = form.watch("username")
  const handleNext = () => {
    setStep(2)
  }
  const { mutate, isPending, isSuccess: isLoginSuccess } = useLogin(handleNext)
  const onSubmit = (values: LoginResponseBody) => {
    mutate({
      username: values.username.toLowerCase(),
      password: values.password
    })
  }
  const onSubmitOtp = (value: string) => {
    mutateOtpVerify({
      username: username.toLowerCase(),
      otp: value
    })
  }
  const resendOtp = () => {
    mutateOtpResend({
      username: username.toLocaleLowerCase(),
    }) 
  }
  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
  };
  useEffect(() => {
    if (callBackUrl || mobileAppScheme || mobileAppPackage) { 
      setAppData({
        ...appData,
        callBackUrl: callBackUrl,
        mobileAppScheme: mobileAppScheme as string,
        mobileAppPackage: mobileAppPackage as string,
        loginReason: loginReason as string
      })
    }
  }, [callBackUrl, mobileAppScheme, mobileAppPackage])
  useEffect(() => {
    if (cookies.accessToken) { 
      const decodedToken: any = jwtDecode(cookies.accessToken);
      // Get current time in seconds
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if the token has expired
      if (currentTime < decodedToken.exp) {
        // If token is still valid, redirect with token data
        if (callBackUrl && callBackUrl !== "null" && callBackUrl !== "") {
          const urlWithToken = callBackUrl.includes('?')
            ? `${callBackUrl}&accessToken=${cookies.accessToken}&refreshToken=${cookies.refreshToken}`
            : `${callBackUrl}?accessToken=${cookies.accessToken}&refreshToken=${cookies.refreshToken}`;
          window.location.href = urlWithToken;
        }
      //   else {
      //    window.location.href = 'https://google.com'
      // } 
      } else {
        // If token has expired, do nothing or clear token
        // // Optionally, you can clear cookies if needed
        // removeCookie('accessToken', { path: '/' }), 
        // removeCookie('refreshToken', { path: '/' })
      }
    }
  },[callBackUrl, cookies, mobileAppScheme] )
  return (
    <main id="safe-focus-element">
      <div className="grid md:grid-cols-2 h-[100vh] relative">
        {/* Left Section */}
        <div className="hidden md:block border-r border-primary-1 p-[4.5rem]">
          <Image src={logo} alt="cafi logo" />
          <h2 className="font-feather text-4xl text-primary-1 mt-36"> {t('login.hero')} </h2>
          {/* <p className="mt-8">
            Kindly verify your email/phone number by inputting your phone number and verification code
          </p> */}
        </div>

        {/* Right Section */}
        <div className=" col-span-1 container mx-auto max-w-[95%] grid h-[90vh] z-20">
          <div className="mt-[2.5rem] md:justify-self-end">
            {/* <div className="flex items-center gap-[0.857rem]  rounded-[0.3125rem] w-fit"> */}
            <Select defaultValue={appData.language}
             onValueChange={(value: string ) => {
              changeLanguage(value)
              setAppData({
              ...appData,
              language: value
            })}}>
              <SelectTrigger className="w-fit">
                <SelectValue placeholder={appData.language === "en" ? 'English' : 'Hausa'}/>
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value='en'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={UkFlag} alt="uk flag"/>
                  <span className="text-[0.875rem] text-gray  "> English </span> 
                  </span>
                </SelectItem>
                <SelectItem value='ha'>
                  <span className="flex items-center gap-[0.35rem]">
                  <Image src={haFlag} alt="uk flag" className="w-[14px] h-[14px]" />
                  <span className="text-[0.875rem] text-gray  "> Hausa </span> 
                  </span>
                </SelectItem>
              </SelectContent>             
            </Select>
            {/* </div>  */}
          </div>
          <div className="flex-grow flex justify-center">
            <div className="w-full relative overflow-hidden">
              <AnimatePresence mode="wait">
                { step === 1 ? (
                    <motion.div
                      key="login-screen"
                      initial={{ x: "-100%", opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: "-100%", opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="w-full flex flex-col"
                      >
                      <div className="grid h-[70vh]">
                      <div className="grid justify-items-center space-y-2">
                        <Image src={logo} alt="cafi logo" className=" md:hidden" />
                        <h1 className="text-center font-fredoka font-[600] text-black-1 text-2xl">{t('login.title')}</h1>
                        <span className="font-blogger text-orange font-bold">{t('login.sub-title')}</span>
                        <p className="text-center font-medium text-gray">{t('login.description')}</p>
                       </div >
                          <Form {...form}>
                            <form className="space-y-4 mt-8">
                              <AppFormItem
                                control={form.control}
                                name="username"
                                inputType="textInput"
                                label={t('login.emailLabel')}
                                placeholder=""
                              />
                              <AppFormItem
                                control={form.control}
                                name="password"
                                inputType="passwordInput"
                                label={t('login.passwordLabel')}
                              />
                            </form>
                          </Form>
                          <AppButton 
                          text={t('login.next')}
                          isLoading={isPending}
                          onClick={form.handleSubmit(onSubmit)}
                          iconSuff={ <Image src={roundLeft} alt="round left" className="" />}
                          className="self-end"
                          />
                        </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="otp-screen"
                      initial={{ x: "100%", opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      exit={{ x: "100%", opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="w-full"
                    >
                    <div className="grid gap-6">
                        <div className="grid justify-items-center space-y-2">
                          <h1 className="text-center font-fredoka font-medium text-black-1 text-2xl"> {t('login.otpTitle')} </h1>
                          <p className="text-center font-medium text-gray">{t('login.otpDescription')} {username}</p>
                        </div>
                          <Form
                          {...otpForm}
                          >
                              {
                                isPendingOtpVerify && isPendingOtpResend ? <div className="grid justify-center items-center"> <div className="w-10 h-10 border-4 border-t-4 border-gray-300 border-t-primary-green rounded-full animate-spin"></div> </div> : ( 
                                <AppFormItem
                                  control={otpForm.control}
                                  name="otp"
                                  inputType="otpInput"
                                  disabled={isPendingOtpResend || isPendingOtpVerify}
                                  otpLength={6} // Customize OTP length
                                  otpOnComplete={(value) => onSubmitOtp(value)}
                                  isNumericOnly={true}
                                  />
                                )
                              }
                         
                            </Form>
                            <OtpTimer
                              initialSeconds={180}
                              isActive={isLoginSuccess}
                              isLoading={isPendingOtpResend || isPendingOtpVerify}
                              onResend={resendOtp}
                              onExpire={resendOtp}
                              />
                        </div>
                    </motion.div>
                  )
                }
              </AnimatePresence>
            </div>
          </div>
        </div>
        
        <div className="absolute w-full h-[35%] bottom-0 z-10">
            <Image src={farmBg} alt="farmbg" className="size-full object-cover" />
        </div>
      </div>
    </main>
  );
}

export default withSplashScreen(EntryPoint);
