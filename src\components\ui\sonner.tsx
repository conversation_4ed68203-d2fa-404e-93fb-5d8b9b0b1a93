"use client"
import Image from "next/image"
import React from "react"
import { useTheme } from "next-themes"
import { Toaster as Sonner, toast as sonnerToast } from "sonner"
import X from "@/assets/icons/x.svg"
// import { X } from "lucide-react"

// Define available variants
export type ToastVariant = "default" | "error" | "success" | "warning"

// Extended toast options to include our variant
interface ExtendedToastOptions {
  variant?: ToastVariant
  description?: string
}

// Toast component props
interface ToasterProps extends React.ComponentProps<typeof Sonner> {
  variant?: ToastVariant
}

// Custom toast function
export const notificationToast = (title: string, options?: ExtendedToastOptions) => {
  const { variant = "default", description, ...rest } = options || {}
  
  // Create JSX content based on variant
  const content = (
    <div className="flex gap-3 w-full">
      {variant === "error" && (
        <div className="flex-shrink-0">
          <Image src={X} alt="x image" />
        </div>
      )}
      <div className="flex flex-col gap-1">
        {title && <div className="font-bold font-blogger">{title}</div>}
        {description && <div className="font-blogger font-medium">{description}</div>}
      </div>
    </div>
  )

  // Apply variant-specific styling through custom className
  const variantClasses = {
    default: "",
    error: "toast-error",
    success: "toast-success",
    warning: "toast-warning"
  }

  return sonnerToast(content, {
    ...rest,
    className: variantClasses[variant],
    // cancel: {
    //   label: 'Cancel',
    //   onClick: () => console.log('Cancel!'),
    // },
  })
}

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()
 
  return (
    <Sonner
      theme={theme as React.ComponentProps<typeof Sonner>["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: `group toast group-[.toaster]:text-foreground group-[.toaster]:border-t-[0px] 
          group-[.toaster]:border-b-[0px] 
          group-[.toaster]:border-r-[0px]
          group-[.toaster]:border-l-[6px] 
          group-[.toaster]:rounded-none 
          group-[.toaster]:rounded-l-[4px]
          group-[.toaster]:shadow-lg`,
          description: "group-[.toast]:text-muted-foreground",
          actionButton: "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton: "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
      }}
      {...props}
      // Add global CSS to handle variant styling
      style={{
        "--toaster-error-bg": "rgba(255, 75, 75, 0.30)",
        "--toaster-error-border": "rgba(255, 75, 75, 1)",
        "--toaster-error-text": "rgba(37, 34, 68, 1)",
        "--toaster-success-bg": "rgba(88, 204, 2, 0.20)",
        "--toaster-success-border": "rgba(84, 204, 0, 1)",
        "--toaster-success-text": "rgba(37, 34, 68, 1)",
        ...props.style,
      } as React.CSSProperties}
    />
  )
}

// Add global CSS to your globals.css file:
// .toast-error {
//   background-color: var(--toaster-error-bg) !important;
//   border-left: 2px solid var(--toaster-error-border) !important;
//   color: var(--toaster-error-text) !important;
// }
// .toast-success {
//   background-color: var(--toaster-success-bg) !important;
//   border-left: 2px solid var(--toaster-success-border) !important;
//   color: var(--toaster-success-text) !important;
// }

export { Toaster }